using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

/// <summary>
/// Handles user invitation operations including sending invitations and validation
/// Manages invitation tokens and verification codes for user registration
/// </summary>
public partial class InvitationFunction : BaseFunctionService
{
    private readonly ILogger<InvitationFunction> _logger;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly IEmailService _emailService;
    private readonly RateLimitService _rateLimitService;

    public InvitationFunction(
        ILogger<InvitationFunction> logger,
        InvitationTokenManager invitationTokenManager,
        IEmailService emailService,
        RateLimitService rateLimitService,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
        _rateLimitService = rateLimitService;
    }

    /// <summary>
    /// Main entry point for invitation operations
    /// Supports: send-invitation, validate-invitation
    /// </summary>
    [Function("InvitationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "invite-user" => await HandleInviteUser(req, correlationId, cancellationToken),
                "validate-token" => await HandleValidateToken(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Invitation service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleInviteUser(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<InvitationRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "invite-user", cancellationToken);
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }


            var token = _invitationTokenManager.GenerateInvitationToken();
            var verificationCode = await _invitationTokenManager.StoreInvitationToken(data.ApplicationName, data.Email, token);
            var emailSent = await _emailService.SendUserInvitationEmailAsync(data.Email, token, verificationCode, data.ApplicationName, data.FirstName ?? "", data.LastName ?? "", correlationId);

            if (emailSent)
            {
                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Invitation sent successfully",
                    correlationId = correlationId
                }, HttpStatusCode.OK, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send invitation email to {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                return await CreateErrorResponse(req, "Failed to send invitation email", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing invitation request [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Error processing invitation request", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleValidateToken(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<TokenValidationRequest>(requestBody, JsonOptions);

            if (data == null || string.IsNullOrEmpty(data.Token))
            {
                return await CreateErrorResponse(req, "Token is required", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "validate-token", cancellationToken);
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            // Use InvitationTokenManager to validate token only (for page access)
            var (isValid, tokenData, errorMessage) = await _invitationTokenManager.ValidateInvitationTokenOnly(data.Token);

            if (!isValid || tokenData == null)
            {
                _logger.LogWarning("Token validation failed: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    errorMessage, correlationId);
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = errorMessage ?? "Invalid or expired invitation token"
                }, HttpStatusCode.Unauthorized, correlationId);
            }

            // Token is valid - grant page access
            _logger.LogInformation("Token validation successful for {Email} [CorrelationId: {CorrelationId}]",
                tokenData.Email, correlationId);

            return await CreateJsonResponse(req, new
            {
                success = true,
                message = "Token is valid",
                email = tokenData.Email,
                applicationId = tokenData.ApplicationId,
                expiresUtc = tokenData.ExpiresUtc
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing token validation [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Error processing token validation", correlationId);
        }
    }

}
